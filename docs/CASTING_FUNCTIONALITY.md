# Video Casting Functionality

This document describes the hybrid casting implementation for Chromecast and AirPlay support in the video downloads feature.

## Overview

The casting functionality allows users to cast downloaded videos to external devices using:
- **Chromecast** - For Android devices and Chrome browsers
- **AirPlay** - For Apple devices and Safari browsers

## Implementation Details

### Hybrid Approach

The implementation uses a hybrid approach that handles different scenarios:

1. **Original URL Available**: If the original video URL is still accessible, it uses that for casting
2. **Original URL Unavailable**: Shows an error message suggesting re-download
3. **AirPlay with Blob URLs**: AirPlay can work with local blob URLs in Safari

### Components

#### 1. `useCasting` Hook (`hooks/useCasting.js`)
- Manages Chromecast and AirPlay state
- Handles Google Cast SDK initialization
- Provides casting functions and error handling

#### 2. `CastableVideoPlayer` Component (`components/CastableVideoPlayer.jsx`)
- Enhanced video player with casting controls
- Shows casting buttons when devices are available
- Handles casting UI and error states

#### 3. Icon Components
- `ui/icons/svg/Chromecast.js` - Chromecast icon
- `ui/icons/svg/AirPlay.js` - AirPlay icon

### Features

#### Chromecast Support
- Automatic device discovery
- Cast session management
- Media metadata support (title, description)
- Error handling for inaccessible URLs

#### AirPlay Support
- Native Safari/WebKit integration
- Works with blob URLs for local videos
- Automatic device detection

#### User Experience
- Floating cast button on video player
- Dropdown menu for device selection
- Status indicators during casting
- Error messages with helpful suggestions

### Usage

The casting functionality is automatically available on the downloads page when:
1. A video is selected for playback
2. Compatible casting devices are detected
3. The browser supports the casting protocols

### Browser Compatibility

#### Chromecast
- Chrome (desktop and mobile)
- Edge (Chromium-based)
- Other Chromium-based browsers

#### AirPlay
- Safari (desktop and mobile)
- WebKit-based browsers on Apple devices

### Error Handling

The system handles various error scenarios:
- No casting devices available
- Original URL no longer accessible
- Network connectivity issues
- Browser compatibility problems

### Technical Requirements

#### Dependencies
- Google Cast SDK (loaded via CDN)
- Native WebKit AirPlay support

#### Network Requirements
- Same local network for all devices
- Internet connectivity for Chromecast setup
- Accessible video URLs for optimal casting

### Future Enhancements

Potential improvements could include:
- Local media server for blob URL casting
- Cloud-assisted casting for inaccessible URLs
- Additional casting protocols (DLNA, Miracast)
- Enhanced metadata and thumbnail support

## Testing

To test the casting functionality:

1. **Chromecast Testing**:
   - Ensure Chromecast device is on same network
   - Use Chrome browser
   - Download a video with accessible original URL
   - Click cast button and select Chromecast

2. **AirPlay Testing**:
   - Use Safari on Mac/iOS device
   - Ensure Apple TV or AirPlay-compatible device is available
   - Download any video (blob URLs work)
   - Click cast button and select AirPlay

## Troubleshooting

### Common Issues

1. **Cast button not appearing**:
   - Check if devices are on same network
   - Verify browser compatibility
   - Ensure Google Cast SDK loaded properly

2. **"Original URL not accessible" error**:
   - Video source may have moved or expired
   - Try re-downloading the video
   - Check internet connectivity

3. **AirPlay not working**:
   - Verify using Safari/WebKit browser
   - Check AirPlay device availability
   - Ensure devices are on same network

### Debug Information

Check browser console for:
- Google Cast SDK loading status
- Network request failures
- JavaScript errors during casting
