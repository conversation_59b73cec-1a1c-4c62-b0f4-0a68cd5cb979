import React, { useState, useEffect, useRef } from 'react'
import dynamic from 'next/dynamic'

import useCasting from '../hooks/useCasting'

const Button = dynamic(() => import('ui/buttons/Button'))
const Icon = dynamic(() => import('ui/icons/Icon'))
const Alert = dynamic(() => import('ui/feedback/Alert'))

// Safari-compatible video component with casting capabilities
export default function CastableVideoPlayer({ video }) {
  const videoRef = useRef(null)
  const [videoSrc, setVideoSrc] = useState(null)
  const [error, setError] = useState(null)
  const [showCastOptions, setShowCastOptions] = useState(false)
  const [castingStatus, setCastingStatus] = useState(null)

  const {
    isCastAvailable,
    isCasting,
    isAirPlayAvailable,
    castError,
    castToChromecast,
    castToAirPlay,
    stopCasting,
    clearError,
  } = useCasting()

  useEffect(() => {
    if (video && video.blob) {
      // Create a fresh blob URL for Safari
      const blobUrl = URL.createObjectURL(video.blob)
      setVideoSrc(blobUrl)
      setError(null)

      // Cleanup function
      return () => {
        URL.revokeObjectURL(blobUrl)
      }
    }
  }, [video])

  const handleError = () => {
    setError(
      'Video playback failed. This may be due to browser compatibility issues.'
    )

    // Try recreating the blob URL
    if (video && video.blob) {
      const newBlobUrl = URL.createObjectURL(video.blob)
      setVideoSrc(newBlobUrl)
    }
  }

  const handleLoadStart = () => {
    setError(null)
  }

  const handleChromecastClick = async () => {
    try {
      setCastingStatus('Connecting to Chromecast...')
      clearError()
      const result = await castToChromecast(video)

      if (result.canceled) {
        // User canceled - just close the options and clear status
        setCastingStatus(null)
        setShowCastOptions(false)
        return
      }

      if (result.success) {
        setCastingStatus('Successfully casting to Chromecast')
        setShowCastOptions(false)

        // Clear status after 3 seconds
        setTimeout(() => setCastingStatus(null), 3000)
      }
    } catch (error) {
      // Check if this was a user cancellation
      if (
        error.code === 'cancel' ||
        error.message?.includes('cancel') ||
        error.message?.includes('Cancel')
      ) {
        // User canceled - just close the options and clear status
        setCastingStatus(null)
        setShowCastOptions(false)
        return
      }

      if (error.message === 'ORIGINAL_URL_NOT_ACCESSIBLE') {
        setCastingStatus(null)
        setError(
          'The original video URL is no longer accessible. To cast this video, you may need to re-download it from the original source.'
        )
      } else {
        setCastingStatus(null)
        setError(`Chromecast error: ${error.message}`)
      }
    }
  }

  const handleAirPlayClick = async () => {
    try {
      setCastingStatus('Opening AirPlay...')
      clearError()
      await castToAirPlay(video, videoRef.current)
      setCastingStatus('AirPlay menu opened')
      setShowCastOptions(false)

      // Clear status after 3 seconds
      setTimeout(() => setCastingStatus(null), 3000)
    } catch (error) {
      setCastingStatus(null)
      setError(`AirPlay error: ${error.message}`)
    }
  }

  const handleStopCasting = async () => {
    try {
      await stopCasting()
      setCastingStatus('Stopped casting')
      setTimeout(() => setCastingStatus(null), 2000)
    } catch (error) {
      setError(`Error stopping cast: ${error.message}`)
    }
  }

  const toggleCastOptions = () => {
    setShowCastOptions(!showCastOptions)
    clearError()
  }

  // Close cast options when clicking outside
  useEffect(() => {
    const handleClickOutside = event => {
      if (showCastOptions && !event.target.closest('.cast-options-container')) {
        setShowCastOptions(false)
      }
    }

    if (showCastOptions) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [showCastOptions])

  if (error) {
    return (
      <div className="flex h-64 items-center justify-center rounded-lg bg-red-50 border border-red-200">
        <div className="text-center p-4">
          <div className="text-red-500 mb-2">
            <svg
              className="mx-auto h-8 w-8"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <p className="text-sm text-red-600 mb-3">{error}</p>
          <div className="space-y-2">
            <button
              onClick={() => setError(null)}
              className="block mx-auto text-xs text-red-500 underline hover:text-red-700"
            >
              Try again
            </button>
            {error.includes('original video URL') && video.originalUrl && (
              <a
                href={video.originalUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="block mx-auto text-xs text-blue-500 underline hover:text-blue-700"
              >
                Open original URL
              </a>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      {/* Video Element */}
      <video
        ref={videoRef}
        controls
        className="h-full w-full"
        src={videoSrc}
        preload="metadata"
        playsInline
        onError={handleError}
        onLoadStart={handleLoadStart}
        // Enable AirPlay for Safari
        x-webkit-airplay="allow"
        // Force re-render if video source changes
        key={videoSrc}
      >
        <track kind="captions" />
        Your browser does not support the video tag.
      </video>

      {/* Casting Controls */}
      <div className="absolute top-2 right-2 flex gap-2">
        {/* Cast Status */}
        {castingStatus && (
          <div className="bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            {castingStatus}
          </div>
        )}

        {/* Cast Button */}
        {(isCastAvailable || isAirPlayAvailable) && (
          <div className="relative cast-options-container">
            <Button
              onClick={toggleCastOptions}
              variant="secondary"
              size="sm"
              className={`bg-black bg-opacity-50 hover:bg-opacity-75 text-white border-none ${
                isCasting ? 'bg-blue-600 bg-opacity-75' : ''
              }`}
              label={
                <Icon
                  name={isCasting ? 'chromecast' : 'tv'}
                  className="w-4 h-4"
                />
              }
              title="Cast video"
            />

            {/* Cast Options Dropdown */}
            {showCastOptions && (
              <div className="absolute top-full right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 min-w-48 z-10">
                <div className="p-2">
                  <div className="text-sm font-medium text-gray-900 mb-2">
                    Cast to device
                  </div>

                  {isCasting && (
                    <Button
                      onClick={handleStopCasting}
                      variant="danger"
                      size="sm"
                      className="w-full mb-2"
                      label="Stop Casting"
                    />
                  )}

                  {isCastAvailable && !isCasting && (
                    <Button
                      onClick={handleChromecastClick}
                      variant="secondary"
                      size="sm"
                      className="w-full mb-2 justify-start"
                      label={
                        <div className="flex items-center gap-2">
                          <Icon name="chromecast" className="w-4 h-4" />
                          <span>Chromecast</span>
                        </div>
                      }
                    />
                  )}

                  {isAirPlayAvailable && !isCasting && (
                    <Button
                      onClick={handleAirPlayClick}
                      variant="secondary"
                      size="sm"
                      className="w-full justify-start"
                      label={
                        <div className="flex items-center gap-2">
                          <Icon name="airplay" className="w-4 h-4" />
                          <span>AirPlay</span>
                        </div>
                      }
                    />
                  )}

                  {!isCastAvailable && !isAirPlayAvailable && (
                    <div className="text-sm text-gray-500 text-center py-2">
                      No casting devices available
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Cast Error Alert */}
      {castError && (
        <div className="absolute bottom-2 left-2 right-2">
          <Alert variant="danger" title="Casting Error">
            {castError}
          </Alert>
        </div>
      )}
    </div>
  )
}
