import { useState, useEffect, useCallback } from 'react'

/**
 * Custom hook for managing Chromecast and AirPlay functionality
 */
export default function useCasting() {
  const [isCastAvailable, setIsCastAvailable] = useState(false)
  const [isCasting, setIsCasting] = useState(false)
  const [castSession, setCastSession] = useState(null)
  const [isAirPlayAvailable, setIsAirPlayAvailable] = useState(false)
  const [castError, setCastError] = useState(null)

  // Initialize Google Cast
  useEffect(() => {
    const initializeCast = () => {
      if (
        typeof window !== 'undefined' &&
        window.chrome &&
        window.chrome.cast &&
        window.cast
      ) {
        const context = window.cast.framework.CastContext.getInstance()

        context.setOptions({
          receiverApplicationId:
            window.chrome.cast.media.DEFAULT_MEDIA_RECEIVER_APP_ID,
          autoJoinPolicy: window.chrome.cast.AutoJoinPolicy.ORIGIN_SCOPED,
        })

        // Listen for cast state changes
        context.addEventListener(
          window.cast.framework.CastContextEventType.CAST_STATE_CHANGED,
          event => {
            const castState = event.castState
            setIsCasting(
              castState === window.cast.framework.CastState.CONNECTED
            )
            setIsCastAvailable(
              castState !== window.cast.framework.CastState.NO_DEVICES_AVAILABLE
            )

            if (castState === window.cast.framework.CastState.CONNECTED) {
              setCastSession(context.getCurrentSession())
            } else {
              setCastSession(null)
            }
          }
        )

        // Initial state
        const castState = context.getCastState()
        setIsCasting(castState === window.cast.framework.CastState.CONNECTED)
        setIsCastAvailable(
          castState !== window.cast.framework.CastState.NO_DEVICES_AVAILABLE
        )
      }
    }

    // Check if Cast SDK is already loaded
    if (typeof window !== 'undefined') {
      if (window.chrome?.cast && window.cast) {
        initializeCast()
      } else {
        // Set up the callback for when Cast SDK loads
        window['__onGCastApiAvailable'] = isAvailable => {
          if (isAvailable) {
            initializeCast()
          }
        }
      }

      // Check for AirPlay availability
      // AirPlay is available on Safari/WebKit browsers
      const isWebKit =
        /WebKit/.test(navigator.userAgent) &&
        !/Chrome/.test(navigator.userAgent)
      setIsAirPlayAvailable(isWebKit)
    }
  }, [])

  // Check if original URL is accessible
  const checkUrlAccessibility = useCallback(async url => {
    try {
      const response = await fetch(url, { method: 'HEAD', mode: 'no-cors' })
      return true
    } catch (error) {
      // Try a simple GET request as fallback
      try {
        const response = await fetch(url, {
          method: 'GET',
          mode: 'no-cors',
          cache: 'no-cache',
        })
        return true
      } catch (fallbackError) {
        return false
      }
    }
  }, [])

  // Cast video to Chromecast
  const castToChromecast = useCallback(
    async video => {
      if (!isCastAvailable || !video || !window.cast || !window.chrome) {
        throw new Error('Chromecast not available')
      }

      setCastError(null)

      try {
        // First, try to use the original URL if available
        let mediaUrl = video.originalUrl
        let canUseOriginalUrl = false

        if (mediaUrl) {
          canUseOriginalUrl = await checkUrlAccessibility(mediaUrl)
        }

        if (!canUseOriginalUrl) {
          throw new Error('ORIGINAL_URL_NOT_ACCESSIBLE')
        }

        const context = window.cast.framework.CastContext.getInstance()

        // Request cast session
        await context.requestSession()

        const session = context.getCurrentSession()
        if (!session) {
          throw new Error('Failed to establish cast session')
        }

        // Create media info
        const mediaInfo = new window.chrome.cast.media.MediaInfo(
          mediaUrl,
          video.type || 'video/mp4'
        )
        mediaInfo.metadata = new window.chrome.cast.media.GenericMediaMetadata()
        mediaInfo.metadata.title = video.title || 'Video'

        const request = new window.chrome.cast.media.LoadRequest(mediaInfo)

        // Load media
        await session.loadMedia(request)

        setCastSession(session)
        setIsCasting(true)

        return { success: true, method: 'chromecast' }
      } catch (error) {
        setCastError(error.message)
        throw error
      }
    },
    [isCastAvailable, checkUrlAccessibility]
  )

  // Cast to AirPlay (uses native browser support)
  const castToAirPlay = useCallback(
    async (video, videoElement) => {
      if (!isAirPlayAvailable || !videoElement) {
        throw new Error('AirPlay not available')
      }

      setCastError(null)

      try {
        // AirPlay can work with blob URLs in Safari
        if (videoElement.webkitShowPlaybackTargetPicker) {
          videoElement.webkitShowPlaybackTargetPicker()
          return { success: true, method: 'airplay' }
        } else {
          throw new Error('AirPlay not supported on this device')
        }
      } catch (error) {
        setCastError(error.message)
        throw error
      }
    },
    [isAirPlayAvailable]
  )

  // Stop casting
  const stopCasting = useCallback(async () => {
    try {
      if (castSession) {
        await castSession.endSession(true)
      }
      setCastSession(null)
      setIsCasting(false)
    } catch (error) {
      setCastError(error.message)
      throw error
    }
  }, [castSession])

  // Clear error
  const clearError = useCallback(() => {
    setCastError(null)
  }, [])

  return {
    isCastAvailable,
    isCasting,
    isAirPlayAvailable,
    castError,
    castToChromecast,
    castToAirPlay,
    stopCasting,
    clearError,
    castSession,
  }
}
