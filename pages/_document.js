import {
  GoogleTagManagerBody,
  GoogleTagManagerHead,
} from 'components/GoogleTagManager'
import Document, { Html, Head, Main, NextScript } from 'next/document'

import { getCustomStyles } from 'ui/helpers/custom-styles/getCustomStyles'
import { getFontFaceRules } from 'ui/helpers/custom-styles/getFontFaceRules'

const imagesCDN = process.env.NEXT_PUBLIC_IMAGES_CDN

export default class MyDocument extends Document {
  render() {
    const { page } = this.props?.__NEXT_DATA__?.props?.pageProps || {}
    const { site } = page || {}
    const siteStyles = getCustomStyles(site) ?? {}
    const fontFaceRules = getFontFaceRules(site?.design?.fonts)

    return (
      <Html>
        <Head>
          <link rel="manifest" href="/site.webmanifest" />
          <meta name="theme-color" content="#ffffff" />
          <link rel="preconnect" href={imagesCDN} crossOrigin="true" />
          <link rel="dns-prefetch" href={imagesCDN} />

          {fontFaceRules && (
            <style dangerouslySetInnerHTML={{ __html: fontFaceRules }} />
          )}

          {/* Google Cast SDK */}
          <script src="https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1" />

          <GoogleTagManagerHead site={site} />
        </Head>
        <body style={siteStyles} className="font-body">
          <GoogleTagManagerBody site={site} />
          <Main />
          <NextScript />
        </body>
      </Html>
    )
  }
}
