import React, { useState, useEffect, useRef } from 'react'
import dynamic from 'next/dynamic'

import { serverSideTranslations } from 'next-i18next/serverSideTranslations'

import useVideoDownload from '../hooks/useVideoDownload'
import useVideoStorage from '../hooks/useVideoStorage'

const Button = dynamic(() => import('ui/buttons/Button'))
const Input = dynamic(() =>
  import('ui/data-entry/Input').then(mod => mod.Input)
)
const Alert = dynamic(() => import('ui/feedback/Alert'))
const CastableVideoPlayer = dynamic(
  () => import('../components/CastableVideoPlayer')
)

// Safari-compatible video component
function SafariVideoPlayer({ video }) {
  const videoRef = useRef(null)
  const [videoSrc, setVideoSrc] = useState(null)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (video && video.blob) {
      // Create a fresh blob URL for Safari
      const blobUrl = URL.createObjectURL(video.blob)
      setVideoSrc(blobUrl)
      setError(null)

      // Cleanup function
      return () => {
        URL.revokeObjectURL(blobUrl)
      }
    }
  }, [video])

  const handleError = () => {
    setError(
      'Video playback failed. This may be due to browser compatibility issues.'
    )

    // Try recreating the blob URL
    if (video && video.blob) {
      const newBlobUrl = URL.createObjectURL(video.blob)
      setVideoSrc(newBlobUrl)
    }
  }

  const handleLoadStart = () => {
    setError(null)
  }

  if (error) {
    return (
      <div className="flex h-64 items-center justify-center rounded-lg bg-red-50 border border-red-200">
        <div className="text-center p-4">
          <div className="text-red-500 mb-2">
            <svg
              className="mx-auto h-8 w-8"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <p className="text-sm text-red-600">{error}</p>
          <button
            onClick={() => setError(null)}
            className="mt-2 text-xs text-red-500 underline hover:text-red-700"
          >
            Try again
          </button>
        </div>
      </div>
    )
  }

  return (
    <video
      ref={videoRef}
      controls
      className="h-full w-full"
      src={videoSrc}
      preload="metadata"
      playsInline
      onError={handleError}
      onLoadStart={handleLoadStart}
    >
      <track kind="captions" />
      Your browser does not support the video tag.
    </video>
  )
}

export default function Downloads() {
  const [videoUrl, setVideoUrl] = useState('')
  const [selectedVideo, setSelectedVideo] = useState(null)

  const { downloadVideo, isDownloading, downloadProgress, downloadError } =
    useVideoDownload()

  const {
    videos,
    deleteVideo,
    storageUsage,
    loading: storageLoading,
    refresh,
  } = useVideoStorage()

  const handleDownload = async () => {
    if (!videoUrl.trim()) return

    try {
      await downloadVideo(videoUrl)
      setVideoUrl('')
    } catch (error) {
      // Error is already handled by the hook
    }
  }

  const handleVideoSelect = video => {
    setSelectedVideo(video)
  }

  const handleDeleteVideo = async videoId => {
    await deleteVideo(videoId)
    if (selectedVideo && selectedVideo.id === videoId) {
      setSelectedVideo(null)
    }
  }

  const handleAddTestVideo = async () => {
    try {
      setVideoUrl(
        'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
      )
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to add test video:', error)
    }
  }

  const formatFileSize = bytes => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStoragePercentage = () => {
    if (!storageUsage?.quota || !storageUsage?.totalUsage) return 0
    return Math.min(100, (storageUsage.totalUsage / storageUsage.quota) * 100)
  }

  const getVideoStoragePercentage = () => {
    if (!storageUsage?.quota || !storageUsage?.used) return 0
    return Math.min(100, (storageUsage.used / storageUsage.quota) * 100)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-4 sm:py-8">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl md:text-4xl">
            Video Downloads
          </h1>
          <p className="mt-2 text-base sm:text-lg text-gray-600">
            Download and store videos locally for offline viewing
          </p>
        </div>

        {/* Download Form */}
        <div className="mb-6 sm:mb-8 rounded-lg bg-white p-4 sm:p-6 shadow-sm">
          <h2 className="mb-4 text-lg sm:text-xl font-semibold text-gray-900">
            Download Video
          </h2>

          {downloadError && (
            <div className="mb-4">
              <Alert variant="danger" title="Download Error">
                {downloadError}
              </Alert>
            </div>
          )}

          <div className="space-y-4">
            {/* URL Input */}
            <div className="w-full">
              <Input
                type="text"
                placeholder="Enter video URL (e.g., https://example.com/video.mp4)"
                value={videoUrl}
                onChange={e => setVideoUrl(e.target.value)}
                className="w-full"
                disabled={isDownloading}
              />
            </div>

            {/* Buttons - responsive layout */}
            <div className="flex flex-col gap-3 sm:flex-row sm:gap-4 sm:justify-start">
              <Button
                onClick={handleDownload}
                disabled={!videoUrl.trim() || isDownloading}
                variant="primary"
                label={isDownloading ? 'Downloading...' : 'Download'}
                className="w-full sm:w-auto"
              />
              <Button
                onClick={handleAddTestVideo}
                variant="secondary"
                label="Add Test Video"
                title="Add a test video for demonstration"
                className="w-full sm:w-auto"
              />
            </div>
          </div>

          {isDownloading && (
            <div className="mt-4">
              <div className="mb-2 flex justify-between text-sm text-gray-600">
                <span>{'Downloading...'}</span>
                <span>{Math.round(downloadProgress)}%</span>
              </div>
              <div
                className="relative h-2 w-full overflow-hidden rounded-full"
                style={{ backgroundColor: '#e5e7eb' }} // gray-200
              >
                <div
                  style={{
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    height: '100%',
                    width: `${Math.max(0, Math.min(100, downloadProgress))}%`,
                    backgroundColor: '#2563eb', // blue-600
                    borderRadius: '9999px',
                    transition: 'width 300ms ease-out',
                    minWidth: downloadProgress > 0 ? '4px' : '0px',
                  }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Storage Usage */}
        {storageUsage && (
          <div className="mb-6 sm:mb-8 rounded-lg bg-white p-4 sm:p-6 shadow-sm">
            <div className="mb-4 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
              <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
                Storage Usage
              </h2>
              <div className="flex justify-end">
                <Button
                  onClick={() => refresh()}
                  variant="tertiary"
                  size="sm"
                  label={storageLoading ? 'Refreshing...' : 'Refresh'}
                  title="Refresh storage usage information"
                  disabled={storageLoading}
                  className="sm:w-auto"
                />
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">
                  {videos.length} video{videos.length !== 1 ? 's' : ''} stored
                </span>
                <span className="font-medium text-gray-900">
                  {formatFileSize(storageUsage.used)} used by videos
                </span>
              </div>

              {storageUsage.quota && (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Total storage used</span>
                    <span className="font-medium text-gray-900">
                      {formatFileSize(storageUsage.totalUsage || 0)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Available storage</span>
                    <span className="font-medium text-gray-900">
                      {formatFileSize(storageUsage.available || 0)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Total quota</span>
                    <span className="font-medium text-gray-900">
                      {formatFileSize(storageUsage.quota)}
                    </span>
                  </div>

                  {/* Storage usage bar */}
                  <div className="mt-4">
                    <div className="mb-2 flex justify-between text-sm text-gray-600">
                      <span>Total Storage Usage</span>
                      <span>{getStoragePercentage().toFixed(1)}%</span>
                    </div>
                    <div
                      className="relative h-2 w-full overflow-hidden rounded-full"
                      style={{ backgroundColor: '#e5e7eb' }} // gray-200
                    >
                      <div
                        style={{
                          position: 'absolute',
                          left: 0,
                          top: 0,
                          height: '100%',
                          width: `${getStoragePercentage()}%`,
                          backgroundColor: '#f59e0b', // amber-500
                          borderRadius: '9999px',
                          transition: 'width 300ms ease-out',
                        }}
                      />
                    </div>

                    {/* Video storage usage bar */}
                    <div className="mt-3">
                      <div className="mb-2 flex justify-between text-sm text-gray-600">
                        <span>Video Storage Usage</span>
                        <span>{getVideoStoragePercentage().toFixed(1)}%</span>
                      </div>
                      <div
                        className="relative h-2 w-full overflow-hidden rounded-full"
                        style={{ backgroundColor: '#e5e7eb' }} // gray-200
                      >
                        <div
                          style={{
                            position: 'absolute',
                            left: 0,
                            top: 0,
                            height: '100%',
                            width: `${getVideoStoragePercentage()}%`,
                            backgroundColor: '#3b82f6', // blue-500
                            borderRadius: '9999px',
                            transition: 'width 300ms ease-out',
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </>
              )}

              {!storageUsage.quota && (
                <div className="text-sm text-gray-500 italic">
                  Storage quota information not available in this browser
                </div>
              )}
            </div>
          </div>
        )}

        <div className="grid gap-6 md:gap-8 lg:grid-cols-3">
          {/* Video List */}
          <div className="lg:col-span-1">
            <div className="rounded-lg bg-white p-4 sm:p-6 shadow-sm">
              <h2 className="mb-4 text-xl font-semibold text-gray-900">
                Downloaded Videos
              </h2>

              {storageLoading ? (
                <div className="text-center text-gray-500">Loading...</div>
              ) : videos.length === 0 ? (
                <div className="text-center text-gray-500">
                  No videos downloaded yet
                </div>
              ) : (
                <div className="space-y-3">
                  {videos.map(video => (
                    <div
                      key={video.id}
                      className={`rounded-lg border p-3 transition-colors ${
                        selectedVideo?.id === video.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <button
                          type="button"
                          className="flex-1 min-w-0 cursor-pointer text-left hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
                          onClick={() => handleVideoSelect(video)}
                        >
                          <h3 className="truncate text-sm font-medium text-gray-900">
                            {video.title || 'Untitled Video'}
                          </h3>
                          <p className="mt-1 text-xs text-gray-500">
                            {formatFileSize(video.size)}
                          </p>
                          <p className="mt-1 text-xs text-gray-400">
                            {new Date(video.downloadedAt).toLocaleDateString()}
                          </p>
                        </button>
                        <div className="ml-3 flex-shrink-0">
                          <Button
                            onClick={() => handleDeleteVideo(video.id)}
                            variant="danger"
                            size="xs"
                            label="Delete"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Video Player */}
          <div className="lg:col-span-2">
            <div className="rounded-lg bg-white p-4 sm:p-6 shadow-sm">
              <h2 className="mb-4 text-xl font-semibold text-gray-900">
                Video Player
              </h2>

              {selectedVideo ? (
                <div>
                  <div className="mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      {selectedVideo.title || 'Untitled Video'}
                    </h3>
                    <p className="text-sm text-gray-500">
                      Downloaded from: {selectedVideo.originalUrl}
                    </p>
                  </div>

                  <div className="aspect-video w-full overflow-hidden rounded-lg bg-black">
                    <CastableVideoPlayer video={selectedVideo} />
                  </div>
                </div>
              ) : (
                <div className="flex h-64 items-center justify-center rounded-lg bg-gray-100">
                  <div className="text-center">
                    <div className="text-gray-400">
                      <svg
                        className="mx-auto h-12 w-12"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      Select a video to play
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'en', ['common'])),
    },
  }
}
